import 'package:flutter/material.dart';
import 'package:get/get.dart';

class NavigationHelper {
  // Smooth navigation to a new screen
  static void navigateTo(Widget page, {
    Transition transition = Transition.rightToLeft,
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
  }) {
    Get.to(
      () => page,
      transition: transition,
      duration: duration,
      curve: curve,
    );
  }

  // Replace current screen with smooth transition
  static void navigateOff(Widget page, {
    Transition transition = Transition.rightToLeft,
    Duration duration = const Duration(milliseconds: 300),
    Curve curve = Curves.easeInOut,
  }) {
    Get.off(
      () => page,
      transition: transition,
      duration: duration,
      curve: curve,
    );
  }

  // Replace all screens with smooth transition
  static void navigateOffAll(Widget page, {
    Transition transition = Transition.fadeIn,
    Duration duration = const Duration(milliseconds: 400),
    Curve curve = Curves.easeInOut,
  }) {
    Get.offAll(
      () => page,
      transition: transition,
      duration: duration,
      curve: curve,
    );
  }

  // Navigate back with check
  static void navigateBack({
    dynamic result,
    bool closeOverlays = false,
  }) {
    if (Navigator.canPop(Get.context!)) {
      Get.back(result: result, closeOverlays: closeOverlays);
    }
  }

  // Navigate to home with proper stack clearing
  static void navigateToHome(Widget homePage) {
    Get.offUntil(
      GetPageRoute(
        page: () => homePage,
        transition: Transition.fadeIn,
        transitionDuration: const Duration(milliseconds: 400),
        curve: Curves.easeInOut,
      ),
      (route) => route.isFirst,
    );
  }

  // Smooth fade transition for auth flows
  static void navigateToAuth(Widget authPage) {
    Get.offAll(
      () => authPage,
      transition: Transition.fadeIn,
      duration: const Duration(milliseconds: 400),
      curve: Curves.easeInOut,
    );
  }

  // Quick navigation for internal app flows
  static void quickNavigateTo(Widget page) {
    Get.to(
      () => page,
      transition: Transition.cupertino,
      duration: const Duration(milliseconds: 250),
    );
  }

  // Slide from bottom for modals/sheets
  static void slideFromBottom(Widget page) {
    Get.to(
      () => page,
      transition: Transition.downToUp,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeOut,
    );
  }
}
